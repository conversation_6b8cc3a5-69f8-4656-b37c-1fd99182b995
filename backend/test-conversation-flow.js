// Test script to trigger conversation flow and debug patient turn issue
const WebSocket = require('ws');

async function testConversationFlow() {
  console.log('🧪 Testing conversation flow...\n');

  // Connect to WebSocket
  const ws = new WebSocket('ws://localhost:3000/ws');

  ws.on('open', () => {
    console.log('✅ Connected to WebSocket');
    
    // Start multi-therapist conversation
    const startMessage = {
      type: 'start_multi_therapist_conversation',
      config: { 
        maxTurns: 3, // Use small number for testing
        personaId: 'emma-anxiety' 
      }
    };
    
    console.log('📤 Sending start message:', JSON.stringify(startMessage, null, 2));
    ws.send(JSON.stringify(startMessage));
  });

  ws.on('message', (data) => {
    const message = JSON.parse(data.toString());
    console.log('📥 Received message:', message.type);
    
    if (message.type === 'differentiated_patient_messages') {
      console.log('🎉 SUCCESS: Received differentiated patient messages!');
      console.log('CBT Patient:', message.data.cbtOnly.message.content.substring(0, 100) + '...');
      console.log('MI Patient:', message.data.miFixedPretreatment.message.content.substring(0, 100) + '...');
      console.log('Dynamic Patient:', message.data.dynamicAdaptive.message.content.substring(0, 100) + '...');
    } else if (message.type === 'multi_therapist_messages') {
      console.log('👩‍⚕️ Received therapist messages');
    } else if (message.type === 'multi_conversation_started') {
      console.log('🚀 Conversation started');
    } else if (message.type === 'comparative_evaluation_complete') {
      console.log('📊 Evaluation complete - conversation ended');
      ws.close();
    }
  });

  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
  });

  ws.on('close', () => {
    console.log('🔌 WebSocket connection closed');
    process.exit(0);
  });

  // Timeout after 30 seconds
  setTimeout(() => {
    console.log('⏰ Test timeout - closing connection');
    ws.close();
  }, 30000);
}

testConversationFlow();
